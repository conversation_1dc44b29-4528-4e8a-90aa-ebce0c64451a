<?php

/**
 * file         : lib/general.class.php
 * author       : doanerkan
 * version      : v:1.0
 * date         : 07/02/2016
 * web          : doanerkan.com
 * mail         : <EMAIL>
 */

    class general extends database
    {

        /**
         * ##################################################
         * # - url path id return
         * ##################################################
         */
        public function de_id($tab)
        {
            $id = $tab;
            $id = explode("-", $id);
            $idc = count($id);
            $id = $id[$idc - 1];
            return $id;
        }

        public function getTagMedia($name, $auth=false, $params=null) {
            return $this->_makeCall('tags/' . $name . '/media/recent', $auth, $params);
        }

        /**
         * ##################################################
         * # - url path seo return
         * ##################################################
         */
        public function de_seo($s)
        {
            $tr = array('ş', 'Ş', 'ı', 'İ', 'ğ', 'Ğ', 'ü', 'Ü', 'ö', 'Ö', 'Ç', 'ç');
            $eng = array('s', 's', 'i', 'i', 'g', 'g', 'u', 'u', 'o', 'o', 'c', 'c');
            $s = str_replace($tr, $eng, $s);
            $s = strtolower($s);
            $s = preg_replace('/&.+?;/', '', $s);
            $s = preg_replace('/[^%a-z0-9 _-]/', '', $s);
            $s = preg_replace('/\s+/', '-', $s);
            $s = preg_replace('|-+|', '-', $s);
            $s = trim($s, '-');
            return $s;
        }

        /**
         * ##################################################
         * # - space variables substr function
         * ##################################################
         */
        public function de_substr($text, $count)
        {
            $kes = substr($text, 0, $count);
            $yazi = explode($kes, $text);
            $kalan = $yazi[1];
            $ekle = explode(" ", $kalan);
            $ekle = $kes . "" . $ekle[0];

            $say = strlen($ekle);
            if ($say > $count) {
                $ekle .= "...";
            }
            return $ekle;
        }

        public function ucwords_tr($gelen){

            $sonuc='';
            $kelimeler=explode(" ", $gelen);

            foreach ($kelimeler as $kelime_duz){

                $kelime_uzunluk=strlen($kelime_duz);
                $ilk_karakter=mb_substr($kelime_duz,0,1,'UTF-8');

                if($ilk_karakter=='Ç' or $ilk_karakter=='ç'){
                    $ilk_karakter='Ç';
                }elseif ($ilk_karakter=='Ğ' or $ilk_karakter=='ğ') {
                    $ilk_karakter='Ğ';
                }elseif($ilk_karakter=='I' or $ilk_karakter=='ı'){
                    $ilk_karakter='I';
                }elseif ($ilk_karakter=='İ' or $ilk_karakter=='i'){
                    $ilk_karakter='İ';
                }elseif ($ilk_karakter=='Ö' or $ilk_karakter=='ö'){
                    $ilk_karakter='Ö';
                }elseif ($ilk_karakter=='Ş' or $ilk_karakter=='ş'){
                    $ilk_karakter='Ş';
                }elseif ($ilk_karakter=='Ü' or $ilk_karakter=='ü'){
                    $ilk_karakter='Ü';
                }else{
                    $ilk_karakter=strtoupper($ilk_karakter);
                }

                $digerleri=mb_substr($kelime_duz,1,$kelime_uzunluk,'UTF-8');
                $sonuc.=$ilk_karakter.$this->kucuk_yap($digerleri).' ';

            }

            $son=trim(str_replace('  ', ' ', $sonuc));
            return $son;

        }

        public function kucuk_yap($gelen){

            $gelen=str_replace('Ç', 'ç', $gelen);
            $gelen=str_replace('Ğ', 'ğ', $gelen);
            $gelen=str_replace('I', 'ı', $gelen);
            $gelen=str_replace('İ', 'i', $gelen);
            $gelen=str_replace('Ö', 'ö', $gelen);
            $gelen=str_replace('Ş', 'ş', $gelen);
            $gelen=str_replace('Ü', 'ü', $gelen);
            $gelen=strtolower($gelen);

            return $gelen;
        }

        /**
         * ##################################################
         * # - date format function
         * ##################################################
         */
        public function de_tarih($text)
        {

            @$yazi = explode(' ', $text);
            @$yazi = explode('-', $yazi[0]);
            $kalan = $yazi[2] . '/' . $yazi[1] . '/' . $yazi[0];
            return $kalan;

        }

        public function de_saat($text)
        {

            @$yazi = explode(' ', $text);
            @$yazi = explode(':', $yazi[1]);
            $kalan = $yazi[0] . ':' . $yazi[1];
            return $kalan;

        }

        /**
         * ##################################################
         * # - logout function
         * ##################################################
         */
        public function de_logout()
        {
            session_destroy();
            header("location:" . admin_URL . "/");
        }

        public function de_logouts()
        {
            session_destroy();
            header("location:" . site_URL . "/");
        }

        public function mailFunction($title,$subject=site_Name,$email,$message,$success='',$file='',$donusemail=''){

            $template   = '
                <!doctype html>
                <html>
                <head>
                <meta http-equiv="content-type" content="text/html" charset="utf-8" />
                <title>'.site_Name.'</title>
                </head><body style="width:100%;margin: 0 auto; background:#f4f4f4;"><br /><br/>
                <div align="center">
                <table width="600" cellpadding="0" cellspacing="0" class="contenttbl" style="font-family:Helvetica, Arial, sans-serif;font-size:12px;color:#787878;background-color:#fff;border-left:1px solid #E1E1E1;border-right:1px solid #E1E1E1"><tbody><tr style="padding:0"><td style="padding:0"><div class="width:562px; height:420px; float:left; display:block;">
                    <p><a href="'.site_URL.'" title="'.site_Name.'" target="_blank"><img src="'.site_Logo.'" style="width:200px; margin:15px 20px 0 20px; display:none;" border="0" /></a></p>
                </div></td></tr><tr style="padding:0"><td style="padding:0 16px 30px 16px; line-height:18px;">
                    <p style="color:#5a5859;padding:0px 16px 0 0px; font:bold 20px Arial;">'.$subject.'</p>
                    '.$message.'
                </td></tr></tbody></table>
                <table width="600" cellpadding="0" cellspacing="0" class="footertbl" style="font-family:Helvetica, Arial, sans-serif;font-size:12px;color:#787878;border-top:1px solid #E1E1E1"><tbody><tr style="padding:0"><td valign="top" align="center" style="padding:0"><table width="594" height="2" cellpadding="0" cellspacing="0" class="sheet" style="font-family:Helvetica, Arial, sans-serif;font-size:12px;color:#787878;background-color:#f8f8f8;border-left:1px solid #E1E1E1;border-right:1px solid #E1E1E1;border-bottom:1px solid #E1E1E1;line-height:1px"><tbody><tr style="padding:0"><td height="2" style="padding:0"></td></tr></tbody></table><table width="588" height="2" cellpadding="0" cellspacing="0" class="sheet" style="font-family:Helvetica, Arial, sans-serif;font-size:12px;color:#787878;background-color:#f8f8f8;border-left:1px solid #E1E1E1;border-right:1px solid #E1E1E1;border-bottom:1px solid #E1E1E1;line-height:1px"><tbody><tr style="padding:0"><td height="2" style="padding:0"></td></tr></tbody></table><table width="552" cellpadding="0" cellspacing="0" style="font-family:Helvetica, Arial, sans-serif;font-size:12px;color:#787878"><tbody><tr style="padding:0"><td class="small" align="left" valign="bottom" style="padding:0"><div style="line-height:16px; color:#787878; font-size:10px; text-align:center;">
                <br />Bu e-posta size '.site_Name.' tarafından gönderilmiştir.</div></td></tr></tbody></table></td></tr></tbody></table></div></body></html>
            ';

            if($donusemail == ''){
                $donusemail = SMTP_reply;
            }
            $mail = new PHPMailer();
            $mail->IsSMTP();
            $mail->SMTPAuth = true;
            $mail->SMTPDebug  = 0;
            $mail->Host = SMTP_server;
            $mail->Port = 587;
            $mail->Username = SMTP_user;
            $mail->Password = SMTP_pass;
            $mail->SetFrom(''.$donusemail.'', site_Name);
            $mail->AddAddress(''.$email.'', ''.$subject.'');
            $mail->CharSet = 'UTF-8';
            $mail->Subject = ''.$subject.'';
            $mail->MsgHTML($template);
            if($mail->Send()){
                if($success != ''){ echo '<div class="alert alert-success formArea">'.$success.'</div>'; }
            }
            else{ echo '<div class="alert alert-danger formArea">Mail gönderilirken bir hata oluştu: ' . $mail->ErrorInfo.'</div>'; }


        }

        /**
         * ##################################################
         * # - sort function / up-down
         * ##################################################
         */
        public function de_sort($sortTable, $sortType, $sortId, $sortQuery = ''){

            $lang = $this->getById('modulemap', '*', "WHERE moduleTableName = '" . $sortTable . "'");
            if ($lang["moduleLang"] != "") {
                $langSql_main = 'AND langRecord = 0';
            } else {
                $langSql_main = '';
            }

            if ($sortType == 'up') {

                $currentSort                    = $this->getById($sortTable, '*', "WHERE id = '" . $sortId . "' " . $sortQuery . "");

                if ($currentSort["sort"] != 1) {

                    $prevRecord                 = $this->getById($sortTable, '*', "WHERE sort = '" . ($currentSort["sort"] - 1) . "' $langSql_main " . $sortQuery . "");

                    $prevRecordUpdate           = $this->getUpdateById($sortTable, "sort = '" . $currentSort["sort"] . "'", "id='" . $prevRecord['id'] . "'");
                    $currRecordUpdate           = $this->getUpdateById($sortTable, "sort = '" . ($currentSort["sort"] - 1) . "'", "id='" . $currentSort['id'] . "'");

                    if ($langSql_main != "") {
                        $prevRecordUpdate       = $this->getUpdateById($sortTable, "sort = '" . $currentSort["sort"] . "'", "langRecord='" . $prevRecord['id'] . "'");
                        $currRecordUpdate       = $this->getUpdateById($sortTable, "sort = '" . ($currentSort["sort"] - 1) . "'", "langRecord='" . $currentSort['id'] . "'");
                    }

                }

            } else {

                $currentSort = $this->getById($sortTable, '*', "WHERE id = '" . $sortId . "' " . $sortQuery . "");

                if ($sortQuery == '') {

                    if ($lang["moduleLang"] != "") {
                        $lastSort               = $this->getById($sortTable, '*', "WHERE $sortQuery langRecord='0' ORDER BY sort DESC");
                    } else {
                        $lastSort               = $this->getById($sortTable, '*', "WHERE $sortQuery ORDER BY sort DESC");
                    }

                } else {

                    if ($lang["moduleLang"] != "") {
                        $lastSort               = $this->getById($sortTable, '*', "WHERE langRecord='0' ORDER BY sort DESC");
                    } else {
                        $lastSort               = $this->getById($sortTable, '*', "ORDER BY sort DESC");
                    }

                }

                if($currentSort["sort"] != $lastSort["sort"]){

                    $nextRecord                 = $this->getById($sortTable, '*', "WHERE sort = '" . ($currentSort["sort"] + 1) . "' $langSql_main " . $sortQuery . "");

                    $nextRecordUpdate           = $this->getUpdateById($sortTable, "sort = '" . $currentSort["sort"] . "'", "id='" . $nextRecord['id'] . "'");
                    $currRecordUpdate           = $this->getUpdateById($sortTable, "sort = '" . ($currentSort["sort"] + 1) . "'", "id='" . $currentSort['id'] . "'");

                    if ($langSql_main != "") {
                        $nextRecordUpdate       = $this->getUpdateById($sortTable, "sort = '" . $currentSort["sort"] . "'", "langRecord='" . $nextRecord['id'] . "'");
                        $currRecordUpdate       = $this->getUpdateById($sortTable, "sort = '" . ($currentSort["sort"] + 1) . "'", "langRecord='" . $currentSort['id'] . "'");
                    }

                }

            }
        }
    }