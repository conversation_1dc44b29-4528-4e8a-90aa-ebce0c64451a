/*
Copyright (c) 2003-2010, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

.cke_skin_office2003 .cke_editor
{
/*
	border: solid 1px #dcdcdc;
*/
	display: inline-table;
	width: 100%;
}

.cke_skin_office2003 span.cke_browser_webkit,
.cke_skin_office2003 span.cke_browser_gecko18
{
	display: block;
}

.cke_skin_office2003,
.cke_skin_office2003 .cke_wrapper
{
	display: block;
}

.cke_skin_office2003 .cke_top,
.cke_skin_office2003 .cke_bottom,
 .cke_shared .cke_skin_office2003
{
	background-color: #f7f8fd;
}

.cke_skin_office2003 .cke_top
{
    border-top: solid 1px #fafaf5;
    border-left: solid 1px #fafaf5;
    border-right: solid 1px #696969;
    border-bottom: solid 2px #696969;
}

.cke_skin_office2003 .cke_rtl .cke_top
{
    border-left: solid 1px #696969;
    border-right: solid 1px #fafaf5;
}

.cke_skin_office2003 .cke_bottom
{
    border-left: solid 1px #696969;
    border-right: solid 1px #696969;
    border-bottom: solid 1px #696969;
}

.cke_skin_office2003 .cke_contents
{
	border: solid 1px #696969;
	/* Prevent background content from penetrate through when switching between editing modes. (#4918) */
	background-color: white;
}

.cke_skin_office2003 .cke_focus
{
	outline: auto 5px -webkit-focus-ring-color;
}

.cke_skin_office2003 textarea.cke_source
{
	font-family: 'Courier New' , Monospace;
	font-size: small;
	white-space: pre;
	background-color: #fff;
}

.cke_skin_office2003 .cke_browser_iequirks textarea.cke_source
{
    /* For IE6+Quirks only */
    _white-space: normal;
}

.cke_skin_office2003 .cke_resizer
{
	width: 12px;
	height: 12px;
	margin-top: 16px;
	display: block;
	float: right;
	/* resizer.gif*/
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 0 -1088px;
	background-repeat: no-repeat;
	cursor: se-resize;
}

.cke_skin_office2003 .cke_rtl .cke_resizer
{
	cursor: sw-resize;
	/* resizer_rtl.gif*/
	background-position: 0 -1115px;
	float: left;
}

.cke_skin_office2003 .cke_resizer_horizontal,
.cke_skin_office2003 .cke_rtl .cke_resizer_horizontal
{
	cursor: e-resize;
}

.cke_skin_office2003 .cke_resizer_vertical,
.cke_skin_office2003 .cke_rtl .cke_resizer_vertical
{
	cursor: n-resize;
}

.cke_skin_office2003 .cke_maximized .cke_resizer
{
	display: none;
}

.cke_skin_office2003 .cke_browser_ie6 .cke_contents textarea,
.cke_skin_office2003 .cke_browser_ie7 .cke_contents textarea
{
	position: absolute;
}

/* All voice labels are not displayed. */
.cke_skin_office2003 .cke_voice_label
{
	display: none;
}

.cke_skin_office2003 legend.cke_voice_label
{
	display: none;
}

.cke_skin_office2003 .cke_browser_ie legend.cke_voice_label
{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
}
