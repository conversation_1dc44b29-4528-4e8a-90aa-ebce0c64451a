@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Light.eot');
    src: url('NeueHaasDisplay-Light.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Black.eot');
    src: url('NeueHaasDisplay-Black.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-BoldItalic.eot');
    src: url('NeueHaasDisplay-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-BoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-BlackItalic.eot');
    src: url('NeueHaasDisplay-BlackItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-BlackItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Bold.eot');
    src: url('NeueHaasDisplay-Bold.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-RomanItalic.eot');
    src: url('NeueHaasDisplay-RomanItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-RomanItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Thin.eot');
    src: url('NeueHaasDisplay-Thin.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-LightItalic.eot');
    src: url('NeueHaasDisplay-LightItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-LightItalic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-ThinItalic.eot');
    src: url('NeueHaasDisplay-ThinItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-ThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Mediu.eot');
    src: url('NeueHaasDisplay-Mediu.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Mediu.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-XThinItalic.eot');
    src: url('NeueHaasDisplay-XThinItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-XThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-MediumItalic.eot');
    src: url('NeueHaasDisplay-MediumItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-XXThin.eot');
    src: url('NeueHaasDisplay-XXThin.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-XXThin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-Roman.eot');
    src: url('NeueHaasDisplay-Roman.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-Roman.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-XThin.eot');
    src: url('NeueHaasDisplay-XThin.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-XThin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Neue Haas Grotesk Display Pro';
    src: url('NeueHaasDisplay-XXThinItalic.eot');
    src: url('NeueHaasDisplay-XXThinItalic.eot?#iefix') format('embedded-opentype'),
        url('NeueHaasDisplay-XXThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

