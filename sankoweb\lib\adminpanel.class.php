<?php

/**
 * file         : lib/adminpanel.class.php
 * author       : doanerkan
 * version      : v:1.0
 * date         : 07/02/2016
 * web          : doanerkan.com
 * mail         : <EMAIL>
 */

header('Content-Type: text/html; charset=utf-8');

class adminpanel extends database
{


    /**
     * @param string $username
     * @param string $password
     * @return array|int
     */
    public function usercheck($username = '', $password = '')
    {

        $username = $this->dataSecurity($username);
        $password = md5($this->dataSecurity($password));

        $user = $this->getById('admin', '*', "WHERE username='$username' AND password='$password'");

        if ($user["username"] == $username && $user["password"] = $password) {
            return $user;
        } else {
            return 0;
        }

    }

    /**
     * @param $id
     * @return array
     */
    public function userInfo($id)
    {
        $user = $this->getById('admin', '*', "WHERE id='" . $id . "'");
        return $user;
    }

    /**
     * @param $id
     */
    public function lastLoginUpdate($id)
    {
        $update = $this->getUpdateById('admin', "lastlogin=now()", 'id=' . $id . '');
    }

    /**
     * @param $dizi
     * @param $area
     * @param $value
     * @return string
     */
    public function jsonAreaValue($dizi, $area, $value)
    {
        foreach ($dizi->$area as $key) {
            return $key->$value . "<br />";
        }
    }

    /**
     * @param $sql
     */
    public function DBTableCreator($sql)
    {
        $query = $this->queryRun($sql);
        return $query;
    }

    /**
     * @param $values
     */
    public function systemModuleMap($values)
    {
        $query = $this->getInsert('modulemap', $values);
        return $query;
    }

    /**
     * @return array
     */
    public function moduleList()
    {
        $query = $this->getAll('modulemap', '*', 'WHERE moduleType=0', 'ORDER BY moduleName ASC');
        return $query;
    }

    /**
     * @return array
     */
    public function moduleAllList()
    {
        $query = $this->getAll('modulemap', '*', '', 'ORDER BY moduleName ASC');
        return $query;
    }

    /**
     * @return array
     */
    public function moduleRelatedList()
    {
        $query = $this->getAll('modulemap', '*', 'WHERE moduleType=2', 'ORDER BY moduleName ASC');
        return $query;
    }

    /**
     * @param $dbName
     * @return mixed
     */
    public function moduleTitleReturn($dbName)
    {
        $query = $this->getById('modulemap', '*', "WHERE moduleTableName = '$dbName'", '');
        return $query;
    }

    /**
     * @param $dbName
     * @return mixed
     */
    public function moduleVeri($dbName){

        $lang   = $this->getById('modulemap', '*', "WHERE moduleTableName = '" . $dbName . "'");

        if($lang["moduleLang"] != ""){
            $query  = $this->getAll($dbName, '*', 'WHERE langRecord=0', 'ORDER BY title ASC');
        }else{
            $query  = $this->getAll($dbName, '*', '', 'ORDER BY title ASC');
        }

        return $query;

    }

    /**
     * @param $dbName
     * @param $filter_row
     * @param $filter_id
     * @param string $sort
     * @param string $specialQuery
     * @return mixed
     */
    public function moduleSelectVeri($dbName, $filter_row, $filter_id, $sort = '', $specialQuery = '')
    {

        if ($specialQuery != '') {

            if ($sort == 1) {
                $query = $this->getAll($dbName, '*', "WHERE $specialQuery", 'ORDER BY sort ASC');
                return $query;
            } else {
                $query = $this->getAll($dbName, '*', "WHERE $specialQuery", 'ORDER BY title ASC');
                return $query;
            }

        } else {

            if ($filter_row != "") {

                if ($sort == 1) {
                    $query = $this->getAll($dbName, '*', "WHERE " . $filter_row . " = '" . $filter_id . "'", 'ORDER BY sort ASC');
                    return $query;
                } else {
                    $query = $this->getAll($dbName, '*', "WHERE " . $filter_row . " = '" . $filter_id . "'", 'ORDER BY title ASC');
                    return $query;
                }

            } else {

                if ($sort == 1) {
                    $query = $this->getAll($dbName, '*', "", 'ORDER BY sort ASC');
                    return $query;
                } else {
                    $query = $this->getAll($dbName, '*', "", 'ORDER BY title ASC');
                    return $query;
                }

            }

        }

    }

    /**
     * @param $dbName
     * @param string $specialQuery
     * @return mixed
     */
    public function moduleByIdVeri($dbName, $specialQuery = '')
    {

        $query = $this->getById($dbName, '*', $specialQuery);
        return $query;

    }

    /**
     * @param $url
     */
    public function moduleInfo($url)
    {
        $url = $this->dataSecurity($url);
        $query = $this->getById('modulemap', '*', "WHERE moduleUrl='$url'");
        return $query;
    }

    /**
     * @param string $dbName
     * @param string $values
     * @return mixed
     */
    public function moduleFirstRecord($dbName = "", $values = "")
    {

        $query = $this->getInsert($dbName, $values);
        return $query;

    }

    /**
     * @param string $dbName
     * @param string $values
     * @param $key
     * @param $record
     * @return mixed
     */
    public function moduleUpdateById($dbName = "", $values = "", $key, $record)
    {

        $query = $this->getUpdateById($dbName, $values, "$key='". $record ."'");
        return $query;

    }

    public function moduleUpdateById2($dbName = "", $values = "", $whereQuery)
    {

        $query = $this->getUpdateById($dbName, $values, "$whereQuery");
        return $query;

    }

    /**
     * @param $dbName
     * @param string $filter
     * @param string $filter_id
     * @param string $lang
     */
    public function sorting($dbName, $filter = "", $filter_id = "", $lang = "")
    {

        if ($filter == "") {
            $query = $this->getAll($dbName, '*', "", 'ORDER BY sort ASC');
        } else {
            $query = $this->getAll($dbName, '*', "WHERE $filter = '$filter_id'", 'ORDER BY sort ASC');
        }
        $jjj = 1;
        foreach ($query as $result) {

            if ($lang != "") {

                if ($result["langRecord"] == 0) {
                    $update = $this->getUpdateById($dbName, "sort='" . $jjj . "'", "id='" . $result["id"] . "'");
                    $update = $this->getUpdateById($dbName, "sort='" . $jjj . "'", "langRecord='" . $result["id"] . "'");
                    $jjj++;
                }

            } else {

                $update = $this->getUpdateById($dbName, "sort='" . $jjj . "'", "id='" . $result["id"] . "'");
                //echo $this->lastQuery;
                $jjj++;

            }
        }

    }

    /**
     * @param $dbName
     * @param $fileType
     * @param $mediaPath
     * @param $record
     * @param $key
     * @param $lang
     */
    public function moduleFileDelete($dbName,$fileType,$mediaPath,$record,$key,$lang){

        $ModulPath      = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/";
        $ModulPathCH    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/main/";
        $ModulPathRB    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/big/";
        $ModulPathRK    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/small/";

        if($lang != ""){
            $langSql        = "OR langRecord='". $record ."'";
        }else{
            $langSql        = "";
        }

        /**
         * ##########
         * DATA QUERY
         * ##########
         */
        $data           = $this->getAll($dbName,'*',"WHERE id = '". $record ."' $langSql");

        foreach($data as $files){

            if($fileType == "file_Resim_Kirpmali"){

                unlink($ModulPathCH.''.$files[$key]);
                unlink($ModulPathRB.''.$files[$key]);
                unlink($ModulPathRK.''.$files[$key]);

            }else if($fileType == "file_Resim_Kirpmasiz" || $fileType == "file_Dosya"){

                unlink($ModulPath.''.$files[$key]);

            }
            // Çoklu resim yükleme de kırpmalı ve kırpmasız resimleri silinecek

        }

    }

    /**
     * @param $dbName
     * @param $record
     * @param $lang
     */
    public function moduleRecordDelete($dbName,$record,$lang){



        if($lang != ""){

            $delete     = $this->getDelete($dbName,"id='". $record ."' OR langRecord='". $record ."'");

        }else{

            $delete     = $this->getDelete($dbName,"id='". $record ."'");

        }

    }

    public function de_seoResim($s){
        $tr = array('ş', 'Ş', 'ı', 'İ', 'ğ', 'Ğ', 'ü', 'Ü', 'ö', 'Ö', 'Ç', 'ç');
        $eng = array('s', 's', 'i', 'i', 'g', 'g', 'u', 'u', 'o', 'o', 'c', 'c');
        $s = str_replace($tr, $eng, $s);
        $s = strtolower($s);
        $s = preg_replace('/&.+?;/', '', $s);
        $s = preg_replace('/[^%a-z0-9 _-]/', '', $s);
        $s = preg_replace('/\s+/', '-', $s);
        $s = preg_replace('|-+|', '-', $s);
        $s = trim($s, '-');
        return $s;
    }

    /**
     * @param string $postFile
     * @param string $fileType
     * @param string $mediaPath
     * @param string $bigWidth
     * @param string $bigHeight
     * @param string $smallWidth
     * @param string $smallHeight
     * @return string
     */
    public function uploadFile($postFile = "", $fileType = "", $mediaPath = "", $bigWidth = "", $bigHeight = "", $smallWidth = "", $smallHeight = "")
    {

        $ModulPath      = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/";
        $ModulPathCH    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/main/";
        $ModulPathRB    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/big/";
        $ModulPathRK    = $_SERVER["DOCUMENT_ROOT"] . "/media/" . $mediaPath . "/small/";

        if ($bigWidth == "" || $fileType == "file_Resim_Kirpmasiz" || $fileType == "file_Dosya") {

            if ($postFile["name"] != "") {


                $Medya          = $postFile;
                @$MedyaType     = $Medya['type'];
                $tr_array       = array("ı", "İ", "ğ", "Ğ", "ü", "Ü", "ş", "Ş", "ç", "Ç", "ö", "Ö", "_", " ");
                $en_array       = array("i", "I", "g", "G", "u", "U", "s", "S", "c", "C", "o", "O", "-", "-");
                $MedyaAdi       = str_replace($tr_array, $en_array, @$Medya['name']);
                $MedyaAdi       = strtolower($MedyaAdi);
                $MedyaAdi 		= preg_replace('@[^A-Za-z0-9\-.]+@i', "", $MedyaAdi);
                $RsmAdi         = sha1(str_replace($tr_array, $en_array, @$Medya['tmp_name']) . time());
                $SavePath       = $ModulPath;
                $MedyaName      = substr($RsmAdi, 1, 6) . '-' . $MedyaAdi;
                $CacheYolu      = $SavePath . $MedyaName;
                copy(@$Medya['tmp_name'], $SavePath . $MedyaName);

            } else {
                $MedyaName      = "";
            }

            return $MedyaName;

        } else {

            if ($postFile["name"] != "") {


                $Medya          = $postFile;
                @$MedyaType     = $Medya['type'];
                $tr_array       = array("ı", "İ", "ğ", "Ğ", "ü", "Ü", "ş", "Ş", "ç", "Ç", "ö", "Ö", "_", " ");
                $en_array       = array("i", "I", "g", "G", "u", "U", "s", "S", "c", "C", "o", "O", "-", "-");
                $MedyaAdi       = str_replace($tr_array, $en_array, @$Medya['name']);
                $MedyaAdi       = strtolower($MedyaAdi);
                $MedyaAdi 		= preg_replace('@[^A-Za-z0-9\-.]+@i', "", $MedyaAdi);
                $RsmAdi         = sha1(str_replace($tr_array, $en_array, @$Medya['tmp_name']) . time());
                $SavePath       = $ModulPathCH;
                $MedyaName      = substr($RsmAdi, 1, 6) . '-' . $MedyaAdi;
                $CacheYolu      = $SavePath . $MedyaName;
                copy(@$Medya['tmp_name'], $SavePath . $MedyaName);

                $KucukResimYolu = $ModulPathRK;
                $KucukResimAl   = $KucukResimYolu . $MedyaName;
                copy($CacheYolu, $KucukResimAl);
                $max_en         = $smallWidth;
                $max_boy        = $smallHeight;
                $icerik         = $this->de_crop($KucukResimAl, $max_en, $max_boy);
                $dosya          = fopen($KucukResimAl, "w+");
                fwrite($dosya, $icerik);
                fclose($dosya);

                $BuyukResimYolu = $ModulPathRB;
                $BuyukResimAl   = $BuyukResimYolu . $MedyaName;
                copy($CacheYolu, $BuyukResimAl);
                $max_en         = $bigWidth;
                $max_boy        = $bigHeight;
                $icerik         = $this->de_crop($BuyukResimAl, $max_en, $max_boy);
                $dosya          = fopen($BuyukResimAl, "w+");
                fwrite($dosya, $icerik);
                fclose($dosya);

            }else{ $MedyaName   = ""; }
            return $MedyaName;

        }

    }

    /**
     * ##################################################
     * # - image crop function
     * ##################################################
     */
    public function de_crop($resim, $max_en, $max_boy)
    {

        ob_start();

        $boyut = getimagesize($resim);
        $en = $boyut[0];
        $boy = $boyut[1];

        # Yeni boyutlar
        $x_oran = $max_en / $en;
        $y_oran = $max_boy / $boy;

        if (($en <= $max_en) and ($boy <= $max_boy)) {
            $son_en = $en;
            $son_boy = $boy;
        } else if (($x_oran * $boy) < $max_boy) {
            $son_en = $max_en;
            $son_boy = ceil($x_oran * $boy);
        } else {
            $son_en = ceil($y_oran * $en);
            $son_boy = $max_boy;
        }

        # Eskı ve yenı resımler
        $bolelim = explode('.', $resim);

        if ($bolelim[count($bolelim) - 1] == 'png' OR $bolelim[count($bolelim) - 1] == 'PNG') {
            $eski = imagecreatefrompng($resim);
        } else if ($bolelim[count($bolelim) - 1] == 'gif' OR $bolelim[count($bolelim) - 1] == 'GIF') {
            $eski = imagecreatefromgif($resim);
        } else {
            $eski = imagecreatefromjpeg($resim);
        }

        $yeni = imagecreatetruecolor($son_en, $son_boy);
        # Eskı resmı yenıden orneklendır
        imagecopyresampled(
            $yeni, $eski, 0, 0, 0, 0,
            $son_en, $son_boy, $en, $boy);

        # Yenı resmı bas ve ıcerıgı cek
        imagejpeg($yeni, null, -1);
        $icerik = ob_get_contents();

        # Resımlerı yoket ve ıcerıgı cıkart
        ob_end_clean();


        return $icerik;

    }
}