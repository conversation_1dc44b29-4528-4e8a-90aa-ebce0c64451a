<?php
/**
 * Dashboard - Ana Kontrol Paneli
 * <PERSON><PERSON><PERSON><PERSON><PERSON>, grafikler ve hızlı erişim
 */

define('CMS_ROOT', __DIR__);
require_once CMS_ROOT . '/config/config.php';

// Page settings
$page_title = 'Dashboard';

// Get dashboard statistics
try {
    global $database;
    $pdo = $database->connect();
    
    // Total counts
    $stats = [];
    
    // Initialize stats array with default values
    $stats = [
        'influencers' => ['total' => 0, 'active' => 0, 'active_this_month' => 0],
        'campaigns' => ['total' => 0, 'completed' => 0, 'in_progress' => 0, 'this_month' => 0, 'growth' => 0],
        'brands' => ['total' => 0],
        'todos' => ['total' => 0, 'pending' => 0, 'completed' => 0],
        'revenue' => ['this_month' => 0, 'growth' => 0],
        'performance' => ['avg_rating' => 0]
    ];

    // Check if tables exist first
    $tables_exist = true;
    $required_tables = ['influencers', 'campaigns', 'brands', 'todos'];

    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $tables_exist = false;
            error_log("Dashboard: Table '$table' does not exist");
            break;
        }
    }

    if ($tables_exist) {
        // Influencers count with better error handling
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total,
                                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                                 FROM influencers");
            $influencer_stats = $stmt->fetch();
            $stats['influencers']['total'] = intval($influencer_stats['total'] ?? 0);
            $stats['influencers']['active'] = intval($influencer_stats['active'] ?? 0);
            error_log("Dashboard: Influencers - Total: {$stats['influencers']['total']}, Active: {$stats['influencers']['active']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching influencers data - " . $e->getMessage());
        }
    
        // Campaigns count with better error handling
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total,
                                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress
                                 FROM campaigns");
            $campaign_stats = $stmt->fetch();
            $stats['campaigns']['total'] = intval($campaign_stats['total'] ?? 0);
            $stats['campaigns']['completed'] = intval($campaign_stats['completed'] ?? 0);
            $stats['campaigns']['in_progress'] = intval($campaign_stats['in_progress'] ?? 0);
            error_log("Dashboard: Campaigns - Total: {$stats['campaigns']['total']}, Completed: {$stats['campaigns']['completed']}, In Progress: {$stats['campaigns']['in_progress']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching campaigns data - " . $e->getMessage());
        }

        // Brands count with better error handling
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM brands WHERE status = 'active'");
            $brand_stats = $stmt->fetch();
            $stats['brands']['total'] = intval($brand_stats['total'] ?? 0);
            error_log("Dashboard: Brands - Total: {$stats['brands']['total']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching brands data - " . $e->getMessage());
        }

        // Todos count with better error handling
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total,
                                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                                 FROM todos");
            $todo_stats = $stmt->fetch();
            $stats['todos']['total'] = intval($todo_stats['total'] ?? 0);
            $stats['todos']['pending'] = intval($todo_stats['pending'] ?? 0);
            $stats['todos']['completed'] = intval($todo_stats['completed'] ?? 0);
            error_log("Dashboard: Todos - Total: {$stats['todos']['total']}, Pending: {$stats['todos']['pending']}, Completed: {$stats['todos']['completed']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching todos data - " . $e->getMessage());
        }

        // Additional stats for reports summary
        // This month campaigns with better error handling
        try {
            $stmt = $pdo->query("
                SELECT COUNT(*) as this_month,
                       (SELECT COUNT(*) FROM campaigns WHERE MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))) as last_month
                FROM campaigns
                WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())
            ");
            $campaign_monthly = $stmt->fetch();
            $stats['campaigns']['this_month'] = intval($campaign_monthly['this_month'] ?? 0);
            $last_month = intval($campaign_monthly['last_month'] ?? 0);
            $stats['campaigns']['growth'] = $last_month > 0 ?
                round((($stats['campaigns']['this_month'] - $last_month) / $last_month) * 100, 1) : 0;
            error_log("Dashboard: This month campaigns: {$stats['campaigns']['this_month']}, Growth: {$stats['campaigns']['growth']}%");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching monthly campaigns - " . $e->getMessage());
        }

        // This month revenue with better error handling
        try {
            $stmt = $pdo->query("
                SELECT COALESCE(SUM(price), 0) as this_month,
                       (SELECT COALESCE(SUM(price), 0) FROM campaigns WHERE MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))) as last_month
                FROM campaigns
                WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())
            ");
            $revenue_monthly = $stmt->fetch();
            $stats['revenue']['this_month'] = floatval($revenue_monthly['this_month'] ?? 0);
            $last_month_revenue = floatval($revenue_monthly['last_month'] ?? 0);
            $stats['revenue']['growth'] = $last_month_revenue > 0 ?
                round((($stats['revenue']['this_month'] - $last_month_revenue) / $last_month_revenue) * 100, 1) : 0;
            error_log("Dashboard: This month revenue: {$stats['revenue']['this_month']}, Growth: {$stats['revenue']['growth']}%");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching monthly revenue - " . $e->getMessage());
        }

        // Active influencers this month with better error handling
        try {
            $stmt = $pdo->query("
                SELECT COUNT(DISTINCT influencer_id) as active_this_month
                FROM campaigns
                WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())
                  AND influencer_id IS NOT NULL
            ");
            $active_result = $stmt->fetch();
            $stats['influencers']['active_this_month'] = intval($active_result['active_this_month'] ?? 0);
            error_log("Dashboard: Active influencers this month: {$stats['influencers']['active_this_month']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching active influencers - " . $e->getMessage());
        }

        // Average rating with better error handling
        try {
            $stmt = $pdo->query("
                SELECT AVG(rating) as avg_rating
                FROM campaigns
                WHERE rating IS NOT NULL AND rating > 0
            ");
            $rating_result = $stmt->fetch();
            $stats['performance']['avg_rating'] = floatval($rating_result['avg_rating'] ?? 0);
            error_log("Dashboard: Average rating: {$stats['performance']['avg_rating']}");
        } catch (Exception $e) {
            error_log("Dashboard: Error fetching average rating - " . $e->getMessage());
        }
    } else {
        // Default values if tables don't exist
        $stats['influencers']['total'] = 0;
        $stats['influencers']['active'] = 0;
        $stats['campaigns']['total'] = 0;
        $stats['campaigns']['completed'] = 0;
        $stats['campaigns']['in_progress'] = 0;
        $stats['brands']['total'] = 0;
        $stats['todos']['total'] = 0;
        $stats['todos']['pending'] = 0;
        $stats['todos']['completed'] = 0;

        // Default values for additional stats
        $stats['campaigns']['this_month'] = 0;
        $stats['campaigns']['growth'] = 0;
        $stats['revenue']['this_month'] = 0;
        $stats['revenue']['growth'] = 0;
        $stats['influencers']['active_this_month'] = 0;
        $stats['performance']['avg_rating'] = 0;
    }

    // Recent activities with better error handling
    $recent_activities = [];
    try {
        // Debug: Check influencer table
        $debug_stmt = $pdo->query("SELECT COUNT(*) as count FROM influencers");
        $debug_count = $debug_stmt->fetch();
        error_log("Dashboard: Total influencers: " . $debug_count['count']);

        $debug_stmt2 = $pdo->query("SELECT COUNT(*) as count FROM influencers WHERE created_at IS NOT NULL");
        $debug_count2 = $debug_stmt2->fetch();
        error_log("Dashboard: Influencers with created_at: " . $debug_count2['count']);

        // Show latest influencers
        $debug_stmt3 = $pdo->query("SELECT first_name, last_name, created_at FROM influencers ORDER BY created_at DESC LIMIT 3");
        $latest = $debug_stmt3->fetchAll();
        foreach ($latest as $inf) {
            error_log("Dashboard: Latest influencer - " . $inf['first_name'] . " " . $inf['last_name'] . " - " . $inf['created_at']);
        }

        // Influencer activities
        $stmt = $pdo->query("
            SELECT
                'influencer_added' as type,
                CONCAT(first_name, ' ', last_name) as title,
                CONCAT('Yeni influencer eklendi: ', first_name, ' ', last_name) as description,
                city as location,
                created_at,
                id as entity_id
            FROM influencers
            WHERE created_at IS NOT NULL
            ORDER BY created_at DESC LIMIT 5
        ");
        $influencer_activities = $stmt->fetchAll();
        error_log("Dashboard: Influencer activities count: " . count($influencer_activities));

        // Campaign activities
        $stmt = $pdo->query("
            SELECT
                'campaign_created' as type,
                title,
                CONCAT('Yeni kampanya oluşturuldu: ', title) as description,
                budget as extra_info,
                created_at,
                id as entity_id
            FROM campaigns
            WHERE created_at IS NOT NULL
            ORDER BY created_at DESC LIMIT 5
        ");
        $campaign_activities = $stmt->fetchAll();
        error_log("Dashboard: Campaign activities count: " . count($campaign_activities));

        // Platform activities (if exists)
        $platform_activities = [];
        if ($pdo->query("SHOW TABLES LIKE 'influencer_platforms'")->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT
                    'platform_added' as type,
                    CONCAT(i.first_name, ' ', i.last_name, ' - ', ip.platform_name) as title,
                    CONCAT(i.first_name, ' ', i.last_name, ' adlı influencer\'a ', ip.platform_name, ' platformu eklendi') as description,
                    CONCAT(FORMAT(ip.followers_count, 0), ' takipçi') as extra_info,
                    ip.created_at,
                    ip.id as entity_id
                FROM influencer_platforms ip
                JOIN influencers i ON ip.influencer_id = i.id
                WHERE ip.created_at IS NOT NULL
                ORDER BY ip.created_at DESC LIMIT 5
            ");
            $platform_activities = $stmt->fetchAll();
        }

        // Merge and sort activities
        $recent_activities = array_merge($influencer_activities, $campaign_activities, $platform_activities);
        error_log("Dashboard: Before sort - Total activities: " . count($recent_activities));

        usort($recent_activities, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        $recent_activities = array_slice($recent_activities, 0, 15);

        error_log("Dashboard: Final activities count: " . count($recent_activities));

        // Debug: Show first activity if exists
        if (!empty($recent_activities)) {
            error_log("Dashboard: First activity - Type: " . $recent_activities[0]['type'] . ", Title: " . $recent_activities[0]['title']);
        }
    } catch (Exception $e) {
        error_log("Dashboard: Error fetching recent activities - " . $e->getMessage());
    }

    // Monthly campaign data for chart with better error handling
    $monthly_campaigns = [];
    try {
        $stmt = $pdo->query("
            SELECT DATE_FORMAT(created_at, '%Y-%m') as month,
                   COUNT(*) as count
            FROM campaigns
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
              AND created_at IS NOT NULL
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month
        ");
        $monthly_campaigns = $stmt->fetchAll();
        error_log("Dashboard: Monthly campaigns data count: " . count($monthly_campaigns));
    } catch (Exception $e) {
        error_log("Dashboard: Error fetching monthly campaigns - " . $e->getMessage());
    }

    // Platform distribution with better error handling
    $platform_distribution = [];
    try {
        $stmt = $pdo->query("
            SELECT platform, COUNT(*) as count
            FROM influencer_platforms
            WHERE platform IS NOT NULL
            GROUP BY platform
            ORDER BY count DESC
        ");
        $platform_distribution = $stmt->fetchAll();
        error_log("Dashboard: Platform distribution count: " . count($platform_distribution));
    } catch (Exception $e) {
        error_log("Dashboard: Error fetching platform distribution - " . $e->getMessage());
    }
    
} catch (Exception $e) {
    error_log('Dashboard error: ' . $e->getMessage());
    $stats = [
        'influencers' => [
            'total' => 0,
            'active' => 0,
            'active_this_month' => 0
        ],
        'campaigns' => [
            'total' => 0,
            'completed' => 0,
            'in_progress' => 0,
            'this_month' => 0,
            'growth' => 0
        ],
        'brands' => ['total' => 0],
        'todos' => ['total' => 0, 'pending' => 0, 'completed' => 0],
        'revenue' => [
            'this_month' => 0,
            'growth' => 0
        ],
        'performance' => [
            'avg_rating' => 0
        ]
    ];
    $recent_activities = [];
    $monthly_campaigns = [];
    $platform_distribution = [];
}

// Include header
include CMS_ROOT . '/includes/header.php';
?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="mb-2">Hoş geldiniz, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h2>
                        <p class="mb-0 opacity-75">
                            Bugün <?php echo date('d F Y, l'); ?> - 
                            Sistemde <?php echo $stats['influencers']['total']; ?> influencer ve 
                            <?php echo $stats['campaigns']['total']; ?> kampanya bulunuyor.
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['influencers']['total']); ?></h3>
                        <p class="text-muted mb-0">Toplam Influencer</p>
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i>
                            <?php echo $stats['influencers']['active']; ?> aktif
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['campaigns']['total']); ?></h3>
                        <p class="text-muted mb-0">Toplam Kampanya</p>
                        <small class="text-info">
                            <i class="fas fa-clock"></i>
                            <?php echo $stats['campaigns']['in_progress']; ?> devam ediyor
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['brands']['total']); ?></h3>
                        <p class="text-muted mb-0">Aktif Marka</p>
                        <small class="text-muted">
                            <i class="fas fa-handshake"></i>
                            İş ortakları
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="row align-items-center">
                <div class="col">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-tasks"></i>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <h3 class="mb-1"><?php echo number_format($stats['todos']['pending']); ?></h3>
                        <p class="text-muted mb-0">Bekleyen Görev</p>
                        <small class="text-success">
                            <i class="fas fa-check"></i>
                            <?php echo $stats['todos']['completed']; ?> tamamlandı
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Quick Reports Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Bu Ay Özet Rapor
                    </h6>
                    <a href="reports.php" class="btn btn-light btn-sm">
                        <i class="fas fa-external-link-alt me-2"></i>Detaylı Rapor
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo $stats['campaigns']['this_month']; ?></h4>
                            <p class="text-muted mb-0">Bu Ay Kampanya</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $stats['campaigns']['growth']; ?>%
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-success mb-1"><?php echo formatCurrency($stats['revenue']['this_month'], 'TRY'); ?></h4>
                            <p class="text-muted mb-0">Bu Ay Gelir</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i>
                                +<?php echo $stats['revenue']['growth']; ?>%
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-info mb-1"><?php echo $stats['influencers']['active_this_month']; ?></h4>
                            <p class="text-muted mb-0">Aktif Influencer</p>
                            <small class="text-info">
                                <i class="fas fa-users"></i>
                                <?php echo $stats['influencers']['total']; ?> toplam
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h4 class="text-warning mb-1"><?php echo number_format($stats['performance']['avg_rating'], 1); ?></h4>
                        <p class="text-muted mb-0">Ortalama Değerlendirme</p>
                        <div class="text-warning">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?php echo $i <= round($stats['performance']['avg_rating']) ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Recent Activities and Quick Actions -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Son Aktiviteler
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_activities)): ?>
                    <div class="timeline">
                        <?php foreach ($recent_activities as $activity): ?>
                            <?php
                            // Determine icon and color based on activity type
                            $icon = 'fas fa-circle';
                            $color = 'primary';
                            $badge_text = '';

                            switch($activity['type']) {
                                case 'influencer_added':
                                    $icon = 'fas fa-user-plus';
                                    $color = 'success';
                                    $badge_text = 'Influencer';
                                    break;
                                case 'campaign_created':
                                    $icon = 'fas fa-bullhorn';
                                    $color = 'warning';
                                    $badge_text = 'Kampanya';
                                    break;
                                case 'platform_added':
                                    $icon = 'fas fa-share-alt';
                                    $color = 'info';
                                    $badge_text = 'Platform';
                                    break;
                            }
                            ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-<?php echo $color; ?>">
                                    <i class="<?php echo $icon; ?> text-white"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                        <span class="badge bg-<?php echo $color; ?> badge-sm"><?php echo $badge_text; ?></span>
                                    </div>

                                    <?php if (isset($activity['description'])): ?>
                                        <p class="mb-2 text-muted small"><?php echo htmlspecialchars($activity['description']); ?></p>
                                    <?php endif; ?>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('d.m.Y H:i', strtotime($activity['created_at'])); ?>
                                        </small>

                                        <?php if (isset($activity['extra_info']) && !empty($activity['extra_info'])): ?>
                                            <small class="text-info">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <?php echo htmlspecialchars($activity['extra_info']); ?>
                                            </small>
                                        <?php endif; ?>

                                        <?php if (isset($activity['location']) && !empty($activity['location'])): ?>
                                            <small class="text-secondary">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?php echo htmlspecialchars($activity['location']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- View All Activities Button -->
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary btn-sm" onclick="loadMoreActivities()">
                            <i class="fas fa-eye me-1"></i>
                            Tüm Aktiviteleri Görüntüle
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Henüz aktivite bulunmuyor</p>
                        <small class="text-muted">Yeni influencer veya kampanya ekleyerek aktivite oluşturabilirsiniz.</small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Hızlı İşlemler
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="influencers.php?action=add" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Yeni Influencer Ekle
                    </a>
                    <a href="campaigns.php?action=add" class="btn btn-success">
                        <i class="fas fa-bullhorn me-2"></i>
                        Yeni Kampanya Oluştur
                    </a>
                    <a href="brands.php?action=add" class="btn btn-warning">
                        <i class="fas fa-building me-2"></i>
                        Yeni Marka Ekle
                    </a>
                    <a href="todos.php?action=add" class="btn btn-info">
                        <i class="fas fa-plus me-2"></i>
                        Yeni Görev Ekle
                    </a>
                </div>
                
                <hr class="my-3">
                
                <h6 class="mb-3">Hızlı Linkler</h6>
                <div class="list-group list-group-flush">
                    <a href="reports.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-chart-bar me-2"></i>Raporları Görüntüle
                    </a>
                    <a href="files.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-folder me-2"></i>Dosya Yönetimi
                    </a>
                    <a href="settings.php" class="list-group-item list-group-item-action border-0 px-0">
                        <i class="fas fa-cog me-2"></i>Sistem Ayarları
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Page specific scripts
$page_scripts = '
<script>
// Campaign Chart
const campaignCtx = document.getElementById("campaignChart").getContext("2d");
const campaignChart = new Chart(campaignCtx, {
    type: "line",
    data: {
        labels: ' . json_encode(array_column($monthly_campaigns, 'month')) . ',
        datasets: [{
            label: "Kampanyalar",
            data: ' . json_encode(array_column($monthly_campaigns, 'count')) . ',
            borderColor: "#667eea",
            backgroundColor: "rgba(102, 126, 234, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: "rgba(0,0,0,0.1)"
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Platform Chart
const platformCtx = document.getElementById("platformChart").getContext("2d");
const platformChart = new Chart(platformCtx, {
    type: "doughnut",
    data: {
        labels: ' . json_encode(array_column($platform_distribution, 'platform')) . ',
        datasets: [{
            data: ' . json_encode(array_column($platform_distribution, 'count')) . ',
            backgroundColor: [
                "#667eea", "#764ba2", "#f093fb", "#f5576c",
                "#4facfe", "#00f2fe", "#43e97b", "#38f9d7"
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: "bottom"
            }
        }
    }
});

// Refresh dashboard data
function refreshDashboardData() {
    // This function can be called to refresh dashboard data via AJAX
    console.log("Refreshing dashboard data...");
}

function loadMoreActivities() {
    // This function can be implemented to load more activities via AJAX
    alert("Tüm aktiviteler özelliği yakında eklenecek!");
}
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline::before {
    content: "";
    position: absolute;
    left: -21px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-content {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s;
    margin-left: 10px;
}

.timeline-content:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.badge-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>
';

// Include footer
include CMS_ROOT . '/includes/footer.php';
?>
