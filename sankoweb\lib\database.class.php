<?php

/**
 * file         : lib/database.class.php
 * author       : doanerkan
 * version      : v:1.0
 * date         : 07/02/2016
 * web          : doanerkan.com
 * mail         : <EMAIL>
 */

    class database{

        protected   $db           = null;
        public      $lastQuery    = null;
        public      $lastInsertId = null;

        /**
         * db constructor.
         */
        public function __construct(){

            if($this->db == null){
                $this->db = new mysqli(db_IP, db_username, db_password, db_table);
                $this->db->set_charset("utf8");
                if ($this->db->connect_error) {
                    die('Connect Error: ' . $mysqli->connect_error);
                }
            }

        }

        /**
         * @param $data
         * @return mixed|string
         */
        public function dataSecurity($data){

            $data = str_replace("'", "’", $data);
            $data = addslashes($data);
            return $data;

        }

        public function dataSecurity2($data){

            $data = str_replace("'", "’", $data);
            $data = addslashes($data);
            return $data;
        }

        /**
         * @param string $table
         * @param string $var
         * @param string $where
         * @param string $limit
         * @return array
         */
        public function getById($table = "", $var = "*", $where = "", $order='', $limit = ""){

            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("SELECT $var FROM $table $where $order $limit");
            $this->lastQuery    = "SELECT $var FROM $table $where $order $limit";

            if($this->db->affected_rows > 0){
                return $row     = $sql->fetch_assoc();
            }else{
                return 0;
            }

        }

        /**
         * @param string $table
         * @param string $var
         * @param string $where
         * @param string $limit
         * @return array
         */
        public function getAll($table = "", $var = "*", $where = "", $order="", $limit = ""){

            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("SELECT $var FROM $table $where $order $limit");
            $this->lastQuery    = "SELECT $var FROM $table $where $order $limit";

            if($this->db->affected_rows > 0){
                $data = array();
                while($row = $sql->fetch_assoc()){
                    $data[] = $row;
                }
                return $data;
            }else {
                return array();
            }

        }

        public function getRows(){

            $sql            = $this->db->affected_rows;
            return $sql;

        }

        /**
         * @param string $table
         * @param string $values
         */
        public function getInsert($table = "", $values = ""){

            //$values = $this->db->real_escape_string($values);

            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("INSERT INTO $table VALUES($values)") or die($this->db->error);
            $this->lastQuery    = "INSERT INTO $table VALUES ($values)";
            $this->lastInsertId = $this->db->insert_id;

        }

        /**
         * @param string $table
         * @param string $set
         * @param string $values
         */
        public function getSetInsert($table = "", $set="", $values = ""){

            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("INSERT INTO $table ($set) VALUES($values)");
            $this->lastQuery    = "INSERT INTO $table ($set) VALUES($values)";
            $this->lastInsertId = $this->db->insert_id;

        }

        /**
         * @param string $table
         * @param string $set
         * @param string $where
         */
        public function getUpdateById($table = "", $set = "", $where = ""){

            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("UPDATE $table SET $set WHERE $where");
            $this->lastQuery    = "UPDATE $table SET $set WHERE $where";
            
        }

        /**
         * @param string $table
         * @param string $set
         */
        public function getUpdateAll($table = "", $set = ""){
            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("UPDATE $table SET $set");
            $this->lastQuery    = "UPDATE $table SET $set";
        }
        
        /**
         * @param string $table
         * @param string $where
         */
        public function getDelete($table = "", $where = ""){
            $table              = db_prefix.''.$table;
            $sql                = $this->db->query("DELETE FROM $table WHERE $where");
            $this->lastQuery    = "DELETE FROM $table WHERE $where";
        }

        /**
         * @return array
         */
        public function databaseTableList(){
            $sql                = $this->db->query("SHOW TABLES FROM ".db_table."");
            $data = array();
            while($row = $sql->fetch_assoc()){
                $data[] = $row;
            }
            return $data;

        }

        public function queryRun($sql){
            if ($this->db->query("$sql") === TRUE) {
                return "Table MyGuests created successfully";
            } else {
                return "Error creating table: " . $this->db->error;
            }
        }

    }