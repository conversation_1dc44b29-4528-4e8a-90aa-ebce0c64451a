/************************ Default CSS ***************************/
::selection {
    color: #fff;
}
::-moz-selection {
    color: #fff;
}
::selection {
    background-color: #223260;
}
::-moz-selection {
    background-color: #223260;
}
.over-hide{
  overflow: hidden;
}

/************************ Animation CSS ***************************/
@keyframes shutters {
  0% {
    clip-path: polygon(0% 0%, 20% 0%, 20% 100%, 20% 100%, 20% 0%, 40% 0%, 40% 100%, 40% 100%, 40% 0%, 60% 0%, 60% 100%, 60% 100%, 60% 0%, 80% 0%, 80% 100%, 80% 100%, 80% 0%, 100% 0%, 100% 100%, 0% 100%);
}
100% {
    clip-path: polygon(20% 0%, 20% 0%, 20% 100%, 40% 100%, 40% 0%, 40% 0%, 40% 100%, 60% 100%, 60% 0%, 60% 0%, 60% 100%, 80% 100%, 80% 0%, 80% 0%, 80% 100%, 100% 100%, 100% 0%, 100% 0%, 100% 100%, 20% 100%);
}
}
@keyframes rotate {
  0% { clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
     border-radius: 100% 45% 65% 100% / 95% 85% 40% 40%;  }
     25% { clip-path: polygon(87.5% 12.5%, 87.5% 87.5%, 12.5% 87.5%, 12.5% 12.5%); }
     50% { clip-path: polygon(75% 75%, 25% 75%, 25% 25%, 75% 25%); }
     75% { clip-path: polygon(37.5% 62.5%, 37.5% 37.5%, 62.5% 37.5%, 62.5% 62.5%); }
     100% { clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%); }
 }
 @keyframes circle {
  0% { clip-path: circle(75%); }
  50% { clip-path: circle(25%); }
  100% { clip-path: circle(75%); }
}
@keyframes color-shape-movement {
  0% { clip-path: polygon(0 0, 50% 0, 100% 0, 100% 50%, 100% 100%, 50% 100%, 0 100%, 0 50%); }
  100% { clip-path:  polygon(50% 50%, 50% 25%, 50% 50%, 75% 50%, 50% 50%, 50% 75%, 50% 50%, 25% 50%); }
}
@keyframes rotate {
  0% { clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%); }
  25% { clip-path: polygon(87.5% 12.5%, 87.5% 87.5%, 12.5% 87.5%, 12.5% 12.5%); }
  50% { clip-path: polygon(75% 75%, 25% 75%, 25% 25%, 75% 25%); }
  75% { clip-path: polygon(37.5% 62.5%, 37.5% 37.5%, 62.5% 37.5%, 62.5% 62.5%); }
  100% { clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%); }
}
@keyframes slots {
  0% {
    clip-path: polygon(0% 0%, 14% 0%, 14% 0%, 28% 0%, 28% 0%, 42% 0%, 42% 0%, 56% 0%, 56% 0%, 70% 0%, 70% 0%, 84% 0%, 84% 0%, 100% 0, 100% 100%, 0% 100%);
}
50% {
    clip-path: polygon(0% 0%, 14% 0%, 14% 100%, 28% 100%, 28% 0%, 42% 0%, 42% 100%, 56% 100%, 56% 0%, 70% 0%, 70% 100%, 84% 100%, 84% 0%, 100% 0, 100% 100%, 0% 100%);
}
100% {
    clip-path: polygon(0% 100%, 14% 100%, 14% 100%, 28% 100%, 28% 100%, 42% 100%, 42% 100%, 56% 100%, 56% 100%, 70% 100%, 70% 100%, 84% 100%, 84% 100%, 100% 100%, 100% 100%, 0% 100%);
}
}
@keyframes morph {
   0% {
       border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
   }
   50% {
       border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
   }
   100% {
       border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
   }
}
/************************ Cursor CSS ***************************/
.cursor {
    --size: 8px;
    height: var(--size);
    width:  var(--size);
    border-radius: 50%;
    position: absolute;
    z-index: 10000;
    transform: translate(-50%, -50%);
    pointer-events: none;
}
.cursor.cursor-shadow {
    background-color: transparent;
    border: 1px solid #223260;
    --size: 40px;
    transition: top .2s, left .2s,
    width .2s, height .2s,
    background-color .2s, border-color 0.2s;
    transition-timing-function: ease-out;
}
.cursor.cursor-dot {
    background: #223260;
    transition: width .2s, height .2s;
}
.cursor-shadow.active {
    --size: 80px;
    border-color: rgba(255,255,255, 0);
    background-color: rgba(255,255,255,.1);
}
.cursor-dot.active {
    --size: 0;
}

