<?php

    class website extends database {

        /**
         *
         * ##### SINGLE RECORD
         *
         * @param string $table
         * @param string $var
         * @param string $where
         * @param string $order
         * @param string $limit
         * @return int
         */
        public function singleRecord($table = "", $var = "*", $where = "", $order="", $limit = ""){

            $data       = $this->getById($table,$var,$where,$order,$limit);
            if ($data != 0) {
                return $data;
            } else {
                return 0;
            }

        }

        /**
         *
         * ##### MULTI RECORD
         *
         * @param string $table
         * @param string $var
         * @param string $where
         * @param string $order
         * @param string $limit
         * @return int
         */
        public function multiRecord($table = "", $var = "*", $where = "", $order="", $limit = ""){

            $data       = $this->getAll($table,$var,$where,$order,$limit);
            return $data;

        }

        /**
         * @param string $table
         * @param string $values
         */
        public function recordInsert($table = "", $values = ""){

            $data       = $this->getInsert($table,$values);
            return $data;

        }

        /**
         * @param string $table
         * @param string $set
         * @param string $where
         */
        public function recordUpdate($table, $set, $where){

            $data       = $this->getUpdateById($table,$set,$where);
            return $data;

        }

        public function SQLClean($inp) {
            $data = str_replace("'", "’", $inp);
            //$data = addslashes($data);
            return $data;
        }

        public function SlashClean($inp) {

            return stripslashes($inp);

        }

    }